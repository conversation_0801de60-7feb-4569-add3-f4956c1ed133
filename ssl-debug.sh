#!/bin/bash
# SSL 调试脚本

CERT_PATH="/home/<USER>/software/certificate/ningmengdou.com.pem"
KEY_PATH="/home/<USER>/software/certificate/ningmengdou.com.key"
DOMAIN="laixi-agr.ningmengdou.com"

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${YELLOW}=== SSL 调试脚本 ===${NC}"
echo ""

# 1. 检查证书文件
echo -e "${YELLOW}1. 检查证书文件...${NC}"
if [ -f "$CERT_PATH" ]; then
    echo -e "${GREEN}✓ 证书文件存在${NC}"
    ls -la "$CERT_PATH"
else
    echo -e "${RED}✗ 证书文件不存在: $CERT_PATH${NC}"
    exit 1
fi

if [ -f "$KEY_PATH" ]; then
    echo -e "${GREEN}✓ 私钥文件存在${NC}"
    ls -la "$KEY_PATH"
else
    echo -e "${RED}✗ 私钥文件不存在: $KEY_PATH${NC}"
    exit 1
fi
echo ""

# 2. 验证证书格式
echo -e "${YELLOW}2. 验证证书格式...${NC}"
if openssl x509 -in "$CERT_PATH" -noout -text >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 证书格式正确${NC}"
    echo "证书信息:"
    openssl x509 -in "$CERT_PATH" -noout -subject -dates
else
    echo -e "${RED}✗ 证书格式错误${NC}"
    exit 1
fi
echo ""

# 3. 验证私钥格式
echo -e "${YELLOW}3. 验证私钥格式...${NC}"
if openssl rsa -in "$KEY_PATH" -check -noout >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 私钥格式正确${NC}"
else
    echo -e "${RED}✗ 私钥格式错误${NC}"
    exit 1
fi
echo ""

# 4. 检查证书和私钥匹配
echo -e "${YELLOW}4. 检查证书和私钥匹配...${NC}"
CERT_MD5=$(openssl x509 -noout -modulus -in "$CERT_PATH" | openssl md5)
KEY_MD5=$(openssl rsa -noout -modulus -in "$KEY_PATH" | openssl md5)

if [ "$CERT_MD5" = "$KEY_MD5" ]; then
    echo -e "${GREEN}✓ 证书和私钥匹配${NC}"
else
    echo -e "${RED}✗ 证书和私钥不匹配${NC}"
    echo "证书 MD5: $CERT_MD5"
    echo "私钥 MD5: $KEY_MD5"
    exit 1
fi
echo ""

# 5. 检查 Nginx 配置
echo -e "${YELLOW}5. 检查 Nginx 配置...${NC}"
if nginx -t >/dev/null 2>&1; then
    echo -e "${GREEN}✓ Nginx 配置正确${NC}"
else
    echo -e "${RED}✗ Nginx 配置错误${NC}"
    nginx -t
    exit 1
fi
echo ""

# 6. 检查端口监听
echo -e "${YELLOW}6. 检查端口监听...${NC}"
if netstat -tlnp | grep -q ":443"; then
    echo -e "${GREEN}✓ 端口 443 正在监听${NC}"
    netstat -tlnp | grep ":443"
else
    echo -e "${RED}✗ 端口 443 未监听${NC}"
    echo "请检查 Nginx 是否正确启动"
    exit 1
fi
echo ""

# 7. 测试本地 SSL 连接
echo -e "${YELLOW}7. 测试本地 SSL 连接...${NC}"
if timeout 5 openssl s_client -connect localhost:443 -servername $DOMAIN </dev/null >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 本地 SSL 连接成功${NC}"
else
    echo -e "${RED}✗ 本地 SSL 连接失败${NC}"
    echo "尝试详细连接测试:"
    timeout 5 openssl s_client -connect localhost:443 -servername $DOMAIN </dev/null
fi
echo ""

# 8. 测试 HTTPS 请求
echo -e "${YELLOW}8. 测试 HTTPS 请求...${NC}"
if curl -k -s -I https://localhost >/dev/null 2>&1; then
    echo -e "${GREEN}✓ HTTPS 请求成功${NC}"
    echo "响应头:"
    curl -k -s -I https://localhost | head -5
else
    echo -e "${RED}✗ HTTPS 请求失败${NC}"
    echo "详细错误:"
    curl -k -v https://localhost 2>&1 | head -10
fi
echo ""

# 9. 检查 Nginx 错误日志
echo -e "${YELLOW}9. 检查 Nginx 错误日志...${NC}"
if [ -f "/var/log/nginx/error.log" ]; then
    echo "最近的错误日志:"
    tail -10 /var/log/nginx/error.log | grep -i ssl
elif [ -f "logs/error.log" ]; then
    echo "最近的错误日志:"
    tail -10 logs/error.log | grep -i ssl
else
    echo -e "${YELLOW}⚠ 未找到错误日志文件${NC}"
fi
echo ""

echo -e "${GREEN}=== 调试完成 ===${NC}"
