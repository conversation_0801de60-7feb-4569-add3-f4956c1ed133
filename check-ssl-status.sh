#!/bin/bash
# SSL 证书状态检查脚本

DOMAIN="ningmengdou.com"
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${YELLOW}检查 $DOMAIN SSL 证书状态...${NC}"
echo "=================================="

# 检查证书文件是否存在
echo -e "${YELLOW}1. 检查证书文件...${NC}"
if [ -f "/etc/ssl/certs/ningmengdou.com.pem" ]; then
    echo -e "${GREEN}✓ 证书文件存在${NC}"
else
    echo -e "${RED}✗ 证书文件不存在${NC}"
fi

if [ -f "/etc/ssl/private/ningmengdou.com.key" ]; then
    echo -e "${GREEN}✓ 私钥文件存在${NC}"
else
    echo -e "${RED}✗ 私钥文件不存在${NC}"
fi

# 检查 Nginx 配置
echo -e "${YELLOW}2. 检查 Nginx 配置...${NC}"
if nginx -t 2>/dev/null; then
    echo -e "${GREEN}✓ Nginx 配置正确${NC}"
else
    echo -e "${RED}✗ Nginx 配置有误${NC}"
fi

# 检查 Nginx 服务状态
echo -e "${YELLOW}3. 检查 Nginx 服务...${NC}"
if systemctl is-active --quiet nginx; then
    echo -e "${GREEN}✓ Nginx 服务运行中${NC}"
else
    echo -e "${RED}✗ Nginx 服务未运行${NC}"
fi

# 检查端口监听
echo -e "${YELLOW}4. 检查端口监听...${NC}"
if netstat -tlnp | grep -q ":443"; then
    echo -e "${GREEN}✓ 端口 443 正在监听${NC}"
else
    echo -e "${RED}✗ 端口 443 未监听${NC}"
fi

if netstat -tlnp | grep -q ":80"; then
    echo -e "${GREEN}✓ 端口 80 正在监听${NC}"
else
    echo -e "${RED}✗ 端口 80 未监听${NC}"
fi

# 检查证书有效期
echo -e "${YELLOW}5. 检查证书有效期...${NC}"
if [ -f "/etc/ssl/certs/ningmengdou.com.pem" ]; then
    EXPIRY=$(openssl x509 -in /etc/ssl/certs/ningmengdou.com.pem -noout -enddate | cut -d= -f2)
    echo "证书到期时间: $EXPIRY"
    
    # 计算剩余天数
    EXPIRY_EPOCH=$(date -d "$EXPIRY" +%s)
    CURRENT_EPOCH=$(date +%s)
    DAYS_LEFT=$(( (EXPIRY_EPOCH - CURRENT_EPOCH) / 86400 ))
    
    if [ $DAYS_LEFT -gt 30 ]; then
        echo -e "${GREEN}✓ 证书还有 $DAYS_LEFT 天到期${NC}"
    elif [ $DAYS_LEFT -gt 7 ]; then
        echo -e "${YELLOW}⚠ 证书还有 $DAYS_LEFT 天到期，建议续期${NC}"
    else
        echo -e "${RED}✗ 证书还有 $DAYS_LEFT 天到期，需要立即续期${NC}"
    fi
fi

# 测试 HTTPS 连接
echo -e "${YELLOW}6. 测试 HTTPS 连接...${NC}"
if curl -s -I https://$DOMAIN >/dev/null 2>&1; then
    echo -e "${GREEN}✓ HTTPS 连接正常${NC}"
else
    echo -e "${RED}✗ HTTPS 连接失败${NC}"
fi

# 检查 HTTP 重定向
echo -e "${YELLOW}7. 检查 HTTP 重定向...${NC}"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://$DOMAIN)
if [ "$HTTP_STATUS" = "301" ] || [ "$HTTP_STATUS" = "302" ]; then
    echo -e "${GREEN}✓ HTTP 正确重定向到 HTTPS${NC}"
else
    echo -e "${RED}✗ HTTP 重定向配置有误 (状态码: $HTTP_STATUS)${NC}"
fi

echo "=================================="
echo -e "${YELLOW}检查完成!${NC}"
