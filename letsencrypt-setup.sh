#!/bin/bash
# Let's Encrypt SSL 证书自动配置脚本

# 安装 Certbot
# Ubuntu/Debian
sudo apt update
sudo apt install certbot python3-certbot-nginx

# CentOS/RHEL
# sudo yum install certbot python3-certbot-nginx

# 获取证书（替换 yourdomain.com 为您的实际域名）
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# 测试自动续期
sudo certbot renew --dry-run

# 设置自动续期 cron 任务
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -

# 检查证书状态
sudo certbot certificates

echo "SSL 证书配置完成！"
echo "请确保您的域名已正确解析到服务器 IP"
