#!/bin/bash
# Nginx 安全重启脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Nginx 重启脚本${NC}"
echo "===================="

# 检查是否以 root 权限运行
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}请以 root 权限运行此脚本${NC}"
    echo "使用: sudo $0"
    exit 1
fi

# 1. 测试配置文件
echo -e "${YELLOW}1. 测试 Nginx 配置文件...${NC}"
if nginx -t; then
    echo -e "${GREEN}✓ 配置文件语法正确${NC}"
else
    echo -e "${RED}✗ 配置文件有错误，停止重启${NC}"
    exit 1
fi

# 2. 备份当前配置（可选）
echo -e "${YELLOW}2. 备份当前配置...${NC}"
BACKUP_DIR="/etc/nginx/backup/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"
cp -r /etc/nginx/sites-enabled/* "$BACKUP_DIR/" 2>/dev/null
echo -e "${GREEN}✓ 配置已备份到 $BACKUP_DIR${NC}"

# 3. 检查当前状态
echo -e "${YELLOW}3. 检查当前 Nginx 状态...${NC}"
if systemctl is-active --quiet nginx; then
    echo -e "${GREEN}✓ Nginx 当前正在运行${NC}"
    NGINX_WAS_RUNNING=true
else
    echo -e "${YELLOW}⚠ Nginx 当前未运行${NC}"
    NGINX_WAS_RUNNING=false
fi

# 4. 选择重启方式
echo -e "${YELLOW}4. 选择重启方式:${NC}"
echo "1) 平滑重载 (reload) - 推荐，不中断服务"
echo "2) 完全重启 (restart) - 会短暂中断服务"
echo "3) 停止后手动启动"
echo "4) 仅测试配置，不重启"

read -p "请选择 (1-4): " choice

case $choice in
    1)
        echo -e "${YELLOW}执行平滑重载...${NC}"
        if systemctl reload nginx; then
            echo -e "${GREEN}✓ Nginx 平滑重载成功${NC}"
        else
            echo -e "${RED}✗ Nginx 平滑重载失败${NC}"
            exit 1
        fi
        ;;
    2)
        echo -e "${YELLOW}执行完全重启...${NC}"
        if systemctl restart nginx; then
            echo -e "${GREEN}✓ Nginx 重启成功${NC}"
        else
            echo -e "${RED}✗ Nginx 重启失败${NC}"
            exit 1
        fi
        ;;
    3)
        echo -e "${YELLOW}停止 Nginx...${NC}"
        systemctl stop nginx
        echo -e "${GREEN}✓ Nginx 已停止${NC}"
        echo -e "${YELLOW}请手动运行以下命令启动:${NC}"
        echo "sudo systemctl start nginx"
        exit 0
        ;;
    4)
        echo -e "${GREEN}✓ 仅测试配置，未重启服务${NC}"
        exit 0
        ;;
    *)
        echo -e "${RED}无效选择${NC}"
        exit 1
        ;;
esac

# 5. 验证重启结果
echo -e "${YELLOW}5. 验证服务状态...${NC}"
sleep 2

if systemctl is-active --quiet nginx; then
    echo -e "${GREEN}✓ Nginx 服务运行正常${NC}"
else
    echo -e "${RED}✗ Nginx 服务未运行${NC}"
    echo -e "${YELLOW}尝试启动服务...${NC}"
    systemctl start nginx
    if systemctl is-active --quiet nginx; then
        echo -e "${GREEN}✓ Nginx 服务启动成功${NC}"
    else
        echo -e "${RED}✗ Nginx 服务启动失败${NC}"
        echo -e "${YELLOW}查看错误日志:${NC}"
        journalctl -u nginx --no-pager -n 10
        exit 1
    fi
fi

# 6. 检查端口监听
echo -e "${YELLOW}6. 检查端口监听...${NC}"
if netstat -tlnp | grep -q ":80"; then
    echo -e "${GREEN}✓ 端口 80 正在监听${NC}"
else
    echo -e "${RED}✗ 端口 80 未监听${NC}"
fi

if netstat -tlnp | grep -q ":443"; then
    echo -e "${GREEN}✓ 端口 443 正在监听${NC}"
else
    echo -e "${YELLOW}⚠ 端口 443 未监听 (如果未配置 SSL 则正常)${NC}"
fi

# 7. 测试网站访问
echo -e "${YELLOW}7. 测试网站访问...${NC}"
if curl -s -I http://localhost >/dev/null 2>&1; then
    echo -e "${GREEN}✓ HTTP 访问正常${NC}"
else
    echo -e "${RED}✗ HTTP 访问失败${NC}"
fi

# 8. 显示服务信息
echo -e "${YELLOW}8. 服务信息:${NC}"
echo "状态: $(systemctl is-active nginx)"
echo "开机自启: $(systemctl is-enabled nginx)"
echo "进程 ID: $(pgrep nginx | head -1)"
echo "配置文件: /etc/nginx/nginx.conf"
echo "错误日志: /var/log/nginx/error.log"
echo "访问日志: /var/log/nginx/access.log"

# 9. 显示有用的命令
echo -e "${BLUE}"
echo "===================="
echo "常用 Nginx 命令:"
echo "===================="
echo -e "${NC}"
echo "查看状态: sudo systemctl status nginx"
echo "查看日志: sudo tail -f /var/log/nginx/error.log"
echo "测试配置: sudo nginx -t"
echo "重新加载: sudo nginx -s reload"
echo "查看进程: sudo ps aux | grep nginx"

echo -e "${GREEN}"
echo "===================="
echo "Nginx 重启完成! 🎉"
echo "===================="
echo -e "${NC}"
