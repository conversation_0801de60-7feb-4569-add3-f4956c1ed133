# Nginx SSL 证书配置指南

## 1. 准备工作

### 域名准备
- 确保域名已解析到服务器 IP
- 准备主域名和子域名（如 api.yourdomain.com）

### 证书获取方式

#### 方式一：Let's Encrypt 免费证书
```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
```

#### 方式二：购买的 SSL 证书
- 将证书文件上传到服务器
- 通常包含：domain.crt, domain.key, domain_bundle.crt

## 2. 证书文件放置

### 推荐目录结构
```
/etc/ssl/
├── certs/
│   ├── yourdomain.com.crt
│   └── yourdomain.com_bundle.crt
└── private/
    └── yourdomain.com.key
```

### 设置正确权限
```bash
sudo chmod 644 /etc/ssl/certs/yourdomain.com.crt
sudo chmod 600 /etc/ssl/private/yourdomain.com.key
sudo chown root:root /etc/ssl/certs/yourdomain.com.crt
sudo chown root:root /etc/ssl/private/yourdomain.com.key
```

## 3. Nginx 配置

### 复制配置文件
```bash
# 复制主配置文件
sudo cp nginx-ssl-config.conf /etc/nginx/sites-available/yourdomain.com

# 或者使用小红书专用配置
sudo cp xhs-nginx-config.conf /etc/nginx/sites-available/xhs-project

# 创建软链接启用站点
sudo ln -s /etc/nginx/sites-available/yourdomain.com /etc/nginx/sites-enabled/

# 删除默认站点（可选）
sudo rm /etc/nginx/sites-enabled/default
```

### 修改配置文件
编辑配置文件，替换以下内容：
- `yourdomain.com` → 您的实际域名
- `/path/to/your/domain.crt` → 实际证书路径
- `/path/to/your/domain.key` → 实际私钥路径
- `127.0.0.1:8000` → 您的 Python 应用端口

## 4. 测试和启动

### 测试 Nginx 配置
```bash
sudo nginx -t
```

### 重新加载 Nginx
```bash
sudo systemctl reload nginx
# 或
sudo service nginx reload
```

### 检查服务状态
```bash
sudo systemctl status nginx
```

## 5. 针对小红书项目的特殊配置

### Python 应用启动
确保您的 Python 应用在指定端口运行：
```bash
# 在您的项目目录中
cd /path/to/your/xhs-project
python3 -m http.server 8000
# 或使用 Flask/Django 等框架
```

### 环境变量设置
```bash
export XHS_DOMAIN="yourdomain.com"
export XHS_SSL_CERT="/etc/ssl/certs/yourdomain.com.crt"
export XHS_SSL_KEY="/etc/ssl/private/yourdomain.com.key"
```

## 6. 安全加固

### 防火墙配置
```bash
# 允许 HTTP 和 HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 如果使用 iptables
sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
```

### SSL 安全测试
使用在线工具测试 SSL 配置：
- https://www.ssllabs.com/ssltest/
- https://www.qualys.com/ssl-labs/

## 7. 监控和维护

### 证书到期监控
```bash
# 检查证书到期时间
openssl x509 -in /etc/ssl/certs/yourdomain.com.crt -noout -dates

# 设置自动续期（Let's Encrypt）
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

### 日志监控
```bash
# 查看访问日志
sudo tail -f /var/log/nginx/yourdomain_access.log

# 查看错误日志
sudo tail -f /var/log/nginx/yourdomain_error.log
```

## 8. 常见问题解决

### 证书链问题
如果浏览器显示证书不受信任，检查是否包含完整证书链：
```bash
# 合并证书和证书链
cat domain.crt domain_bundle.crt > domain_full.crt
```

### 权限问题
```bash
# 确保 Nginx 用户可以读取证书
sudo chown -R www-data:www-data /var/www/
sudo chmod -R 755 /var/www/
```

### 端口冲突
```bash
# 检查端口占用
sudo netstat -tlnp | grep :443
sudo lsof -i :443
```

## 9. 性能优化

### 启用 HTTP/2
确保配置中包含 `http2` 参数：
```nginx
listen 443 ssl http2;
```

### 启用 Gzip 压缩
在 nginx.conf 中添加：
```nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
```

### 缓存优化
```nginx
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

完成以上配置后，您的网站应该可以通过 HTTPS 安全访问了！
