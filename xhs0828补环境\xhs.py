import json
import os
import execjs
import requests

headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "cache-control": "no-cache",
    "content-type": "application/json;charset=UTF-8",
    "origin": "https://www.xiaohongshu.com",
    "pragma": "no-cache",
    "referer": "https://www.xiaohongshu.com/",
    "sec-ch-ua": "\"Chromium\";v=\"112\", \"Google Chrome\";v=\"112\", \"Not:A-Brand\";v=\"99\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-site",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
}


def extract_cookie_value(cookie, key):
    # 将 cookie 字符串转换为字典
    cookies_dict = dict(item.split('=', 1) for item in cookie.split(';'))
    # 返回指定 key 的值
    return cookies_dict.get(key.strip())


def sentPostRequest(host, api, data, cookie):
    if cookie == "":
        print("need cookie")
        return

    current_directory = os.path.dirname(__file__)
    file_path = os.path.join(current_directory, "xhs.js")
    xs_xt = execjs.compile(open(file_path, 'r', encoding='utf-8').read()).call('getXs', api, data, extract_cookie_value(cookie, 'a1'))
    print(xs_xt)
    headers['cookie'] = cookie
    headers['X-s'] = xs_xt['X-s']
    headers['X-t'] = str(xs_xt['X-t'])

    url = host + api
    response = requests.post(url=url, data=json.dumps(data, separators=(",", ":"), ensure_ascii=False).encode("utf-8"), headers=headers)

    return response.json()
def DoApi(param, cookie):
    api = '/api/sns/web/v1/comment/post'
    host = 'https://edith.xiaohongshu.com'
    data = {
        "note_id": param["note_id"],
        "content": param["content"],
        "at_users":  param["at_users"],
    }
    return sentPostRequest(host, api, data, cookie)




if __name__ == '__main__':
    # 需要安装python pyexecjs，requests库, 否则会报错。
    # pip install pyexecjs requests
    # cookie 里a1，websession参数重要，其它不重要。
    cookie = "a1=190d42d25a8wptni8mcj5pr8fpcxbb9d9295m3erx30000926220;web_session=040069b21a98c32f883e7558e5344ba4d458bb"  # put your cookie here
    # cookie = "a1=190d42d25a8wptni8mcj5pr8fpcxbb9d9295m3erx30000926220;webId=3a254f84f211e37cf71adb47b28ca47e;gid=yj8f4Jfq42j0yj8f4JfJ2Jqd0YEU9I3Y6SV2jFCUFYiUS2q8CDDjf6888jJKJJ88DJ2SSf0y;webBuild=4.29.0;websectiga=6169c1e84f393779a5f7de7303038f3b47a78e47be716e7bec57ccce17d45f99;sec_poison_id=ade77c9a-3f05-433b-9947-010e562202a4;acw_tc=0648bb6918e5fe337d7c789f15e6a077bb387d69ebff53e0a1c2a2d46d40612e;web_session=040069b21a98c32f883eb71cf4344bfa12f81a;unread={%22ub%22:%22669dda0e0000000027010f66%22%2C%22ue%22:%22669b13fb0000000005005618%22%2C%22uc%22:17};xsecappid=xhs-pc-web" # put your cookie here
    # 向笔记发送评论demo
    param = {
        "note_id": "66ac52ae000000000d03364a",
        "content": "你好",
        "at_users":  []
    }

    response = DoApi(param,cookie)
    print(response)

