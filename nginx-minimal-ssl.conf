user  mengdou;
worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    # 最简单的 HTTPS 配置测试
    server {
        listen       443 ssl;
        server_name  laixi-agr.ningmengdou.com;

        # SSL 证书配置
        ssl_certificate /home/<USER>/software/certificate/ningmengdou.com.pem;
        ssl_certificate_key /home/<USER>/software/certificate/ningmengdou.com.key;

        # 最基本的 SSL 设置
        ssl_protocols TLSv1.2;
        ssl_session_timeout 5m;

        # 简单的测试页面
        location / {
            return 200 "HTTPS 工作正常!\n";
            add_header Content-Type text/plain;
        }
    }

    # HTTP 服务器
    server {
        listen       80;
        server_name  laixi-agr.ningmengdou.com;

        location / {
            return 200 "HTTP 工作正常!\n";
            add_header Content-Type text/plain;
        }
    }
}
