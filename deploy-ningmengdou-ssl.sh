#!/bin/bash
# ningmengdou.com SSL 证书部署脚本

echo "开始部署 ningmengdou.com SSL 证书..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查是否以 root 权限运行
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}请以 root 权限运行此脚本${NC}"
    echo "使用: sudo $0"
    exit 1
fi

# 配置变量
DOMAIN="ningmengdou.com"
CERT_DIR="/etc/ssl/certs"
KEY_DIR="/etc/ssl/private"
NGINX_SITES_AVAILABLE="/etc/nginx/sites-available"
NGINX_SITES_ENABLED="/etc/nginx/sites-enabled"
WEB_ROOT="/var/www/ningmengdou"

# 创建必要的目录
echo -e "${YELLOW}创建必要的目录...${NC}"
mkdir -p $CERT_DIR
mkdir -p $KEY_DIR
mkdir -p $WEB_ROOT
mkdir -p /var/log/nginx

# 复制证书文件（假设证书文件在当前目录）
echo -e "${YELLOW}复制证书文件...${NC}"
if [ -f "ningmengdou.com.pem" ] && [ -f "ningmengdou.com.key" ]; then
    cp ningmengdou.com.pem $CERT_DIR/
    cp ningmengdou.com.key $KEY_DIR/
    
    # 设置正确的权限
    chmod 644 $CERT_DIR/ningmengdou.com.pem
    chmod 600 $KEY_DIR/ningmengdou.com.key
    chown root:root $CERT_DIR/ningmengdou.com.pem
    chown root:root $KEY_DIR/ningmengdou.com.key
    
    echo -e "${GREEN}证书文件复制完成${NC}"
else
    echo -e "${RED}错误: 找不到证书文件 ningmengdou.com.pem 或 ningmengdou.com.key${NC}"
    echo "请确保证书文件在当前目录中"
    exit 1
fi

# 更新 Nginx 配置文件中的路径
echo -e "${YELLOW}更新 Nginx 配置文件...${NC}"
sed -i "s|/path/to/certificates/ningmengdou.com.pem|$CERT_DIR/ningmengdou.com.pem|g" ningmengdou-nginx.conf
sed -i "s|/path/to/certificates/ningmengdou.com.key|$KEY_DIR/ningmengdou.com.key|g" ningmengdou-nginx.conf

# 复制 Nginx 配置文件
cp ningmengdou-nginx.conf $NGINX_SITES_AVAILABLE/$DOMAIN

# 创建软链接启用站点
if [ -L "$NGINX_SITES_ENABLED/$DOMAIN" ]; then
    echo -e "${YELLOW}站点已启用，跳过创建软链接${NC}"
else
    ln -s $NGINX_SITES_AVAILABLE/$DOMAIN $NGINX_SITES_ENABLED/
    echo -e "${GREEN}站点已启用${NC}"
fi

# 禁用默认站点（可选）
if [ -L "$NGINX_SITES_ENABLED/default" ]; then
    echo -e "${YELLOW}是否禁用默认站点? (y/n)${NC}"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        rm $NGINX_SITES_ENABLED/default
        echo -e "${GREEN}默认站点已禁用${NC}"
    fi
fi

# 创建基本的网站文件
if [ ! -f "$WEB_ROOT/index.html" ]; then
    cat > $WEB_ROOT/index.html << EOF
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ningmengdou.com - SSL 配置成功</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
        .container { max-width: 600px; margin: 0 auto; }
        .success { color: #28a745; }
        .info { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="success">🎉 SSL 证书配置成功!</h1>
        <p>欢迎访问 ningmengdou.com</p>
        <div class="info">
            <h3>配置信息</h3>
            <p>域名: ningmengdou.com</p>
            <p>SSL 状态: ✅ 已启用</p>
            <p>HTTP/2: ✅ 已启用</p>
            <p>配置时间: $(date)</p>
        </div>
        <p><a href="/api/health">API 健康检查</a></p>
    </div>
</body>
</html>
EOF
    echo -e "${GREEN}创建了默认首页${NC}"
fi

# 设置网站目录权限
chown -R www-data:www-data $WEB_ROOT
chmod -R 755 $WEB_ROOT

# 测试 Nginx 配置
echo -e "${YELLOW}测试 Nginx 配置...${NC}"
if nginx -t; then
    echo -e "${GREEN}Nginx 配置测试通过${NC}"
    
    # 重新加载 Nginx
    echo -e "${YELLOW}重新加载 Nginx...${NC}"
    systemctl reload nginx
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Nginx 重新加载成功${NC}"
    else
        echo -e "${RED}Nginx 重新加载失败${NC}"
        exit 1
    fi
else
    echo -e "${RED}Nginx 配置测试失败，请检查配置文件${NC}"
    exit 1
fi

# 检查 Nginx 状态
echo -e "${YELLOW}检查 Nginx 服务状态...${NC}"
if systemctl is-active --quiet nginx; then
    echo -e "${GREEN}Nginx 服务运行正常${NC}"
else
    echo -e "${RED}Nginx 服务未运行，尝试启动...${NC}"
    systemctl start nginx
fi

# 检查防火墙设置
echo -e "${YELLOW}检查防火墙设置...${NC}"
if command -v ufw &> /dev/null; then
    ufw allow 80/tcp
    ufw allow 443/tcp
    echo -e "${GREEN}防火墙规则已更新 (ufw)${NC}"
elif command -v firewall-cmd &> /dev/null; then
    firewall-cmd --permanent --add-service=http
    firewall-cmd --permanent --add-service=https
    firewall-cmd --reload
    echo -e "${GREEN}防火墙规则已更新 (firewalld)${NC}"
fi

# 验证证书
echo -e "${YELLOW}验证 SSL 证书...${NC}"
echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -dates

# 显示完成信息
echo -e "${GREEN}"
echo "=================================="
echo "SSL 证书部署完成!"
echo "=================================="
echo -e "${NC}"
echo "域名: https://$DOMAIN"
echo "证书位置: $CERT_DIR/ningmengdou.com.pem"
echo "私钥位置: $KEY_DIR/ningmengdou.com.key"
echo "网站根目录: $WEB_ROOT"
echo "Nginx 配置: $NGINX_SITES_AVAILABLE/$DOMAIN"
echo ""
echo -e "${YELLOW}下一步:${NC}"
echo "1. 确保域名 DNS 解析到此服务器"
echo "2. 访问 https://$DOMAIN 测试"
echo "3. 启动您的 Python 应用在端口 8000"
echo "4. 使用 SSL Labs 测试: https://www.ssllabs.com/ssltest/"
echo ""
echo -e "${GREEN}部署完成! 🎉${NC}"
