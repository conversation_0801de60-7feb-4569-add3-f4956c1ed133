user  mengdou;
worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    sendfile        on;
    keepalive_timeout  65;

    # 简单的 HTTP 测试服务器
    server {
        listen       80;
        server_name  laixi-agr.ningmengdou.com;

        location / {
            root /home/<USER>/project/index/dist/;
            try_files $uri $uri/ /index.html;
        }

        location ^~/api {
            proxy_pass http://localhost:8080;
        }
    }

    # 简单的 HTTPS 测试服务器
    server {
        listen       443 ssl;
        server_name  laixi-agr.ningmengdou.com;

        # SSL 证书配置
        ssl_certificate /home/<USER>/software/certificate/ningmengdou.com.pem;
        ssl_certificate_key /home/<USER>/software/certificate/ningmengdou.com.key;

        # 基本 SSL 配置
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_session_cache shared:SSL:1m;
        ssl_session_timeout 5m;

        location / {
            root /home/<USER>/project/index/dist/;
            try_files $uri $uri/ /index.html;
        }

        location ^~/api {
            proxy_pass http://localhost:8080;
        }
    }
}
