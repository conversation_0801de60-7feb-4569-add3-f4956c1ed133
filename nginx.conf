
user  root;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for" "$request_filename"';

    access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    #gzip  on;

    server {
        listen       80;
        server_name  laixi-agr.ningmengdou.com;

        #charset koi8-r;

        #access_log  logs/host.access.log  main;

#         location / {
#             root   html;
#             index  index.html index.htm;
#         }

        location / {
            root /home/<USER>/project/index/dist/;
            try_files $uri $uri/ /index.html;
        }

        location ^~/api {
            # 允许所有来源的请求
            add_header 'Access-Control-Allow-Origin' $http_origin;
            # 允许所有请求头
            add_header 'Access-Control-Allow-Headers' '*';
            # 允许所有HTTP方法
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
            # 允许携带凭证（如Cookie）
            add_header 'Access-Control-Allow-Credentials' 'true';
            # 处理预检请求
            if ($request_method = 'OPTIONS') {
                # 允许所有来源的请求
                add_header 'Access-Control-Allow-Origin' $http_origin;
                # 允许所有请求头
                add_header 'Access-Control-Allow-Headers' '*';
                # 允许所有HTTP方法
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Length' 0;
                add_header 'Content-Type' 'text/plain;charset=UTF-8';
                return 204;
            }
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_buffering on;
            rewrite ^/api/(.*)$ /$1 break;
            proxy_pass http://localhost:8080;
        }

        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
   # HTTPS 服务器 - 主站点
    server {
        listen       443 ssl;
        server_name  laixi-agr.ningmengdou.com;

        # SSL 证书配置
        ssl_certificate /etc/nginx/ssl/ningmengdou.com.pem;
        ssl_certificate_key /etc/nginx/ssl/ningmengdou.com.key;

        # 现代 SSL 配置
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 1d;
        ssl_session_tickets off;

        # 安全头
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;

        # 网站根目录
        location / {
            root /home/<USER>/project/index/dist/;
            try_files $uri $uri/ /index.html;
            
            # 静态文件缓存
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
                access_log off;
            }
        }

        # API 代理配置
        location ^~/api {
            # CORS 配置
            add_header 'Access-Control-Allow-Origin' $http_origin always;
            add_header 'Access-Control-Allow-Headers' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;
            
            # 处理预检请求
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' $http_origin;
                add_header 'Access-Control-Allow-Headers' '*';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Length' 0;
                add_header 'Content-Type' 'text/plain;charset=UTF-8';
                return 204;
            }
            
            # 代理设置
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_buffering on;
            
            # 超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            rewrite ^/api/(.*)$ /$1 break;
            proxy_pass http://localhost:8080;
        }

        # 错误页面
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }

    server {
        listen       81;
        server_name  laixi-agr.ningmengdou.com;

        # 注意：端口 81 是 HTTP，不需要 SSL 配置
        # 如果需要 HTTPS，应该使用不同的端口如 8443

        location / {
            root /home/<USER>/project/qym/;
            try_files $uri $uri/ /index.html;
        }

        location ^~/api {
            # 允许所有来源的请求
            add_header 'Access-Control-Allow-Origin' $http_origin;
            # 允许所有请求头
            add_header 'Access-Control-Allow-Headers' '*';
            # 允许所有HTTP方法
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
            # 允许携带凭证（如Cookie）
            add_header 'Access-Control-Allow-Credentials' 'true';
            # 处理预检请求
            if ($request_method = 'OPTIONS') {
                # 允许所有来源的请求
                add_header 'Access-Control-Allow-Origin' $http_origin;
                # 允许所有请求头
                add_header 'Access-Control-Allow-Headers' '*';
                # 允许所有HTTP方法
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Length' 0;
                add_header 'Content-Type' 'text/plain;charset=UTF-8';
                return 204;
            }
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_buffering on;
            rewrite ^/api/(.*)$ /$1 break;
            proxy_pass http://localhost:8080;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }


    # another virtual host using mix of IP-, name-, and port-based configuration
    #
    #server {
    #    listen       8000;
    #    listen       somename:8080;
    #    server_name  somename  alias  another.alias;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}


    # HTTPS server
    #
    #server {
    #    listen       443 ssl;
    #    server_name  localhost;

    #    ssl_certificate      cert.pem;
    #    ssl_certificate_key  cert.key;

    #    ssl_session_cache    shared:SSL:1m;
    #    ssl_session_timeout  5m;

    #    ssl_ciphers  HIGH:!aNULL:!MD5;
    #    ssl_prefer_server_ciphers  on;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}

}
