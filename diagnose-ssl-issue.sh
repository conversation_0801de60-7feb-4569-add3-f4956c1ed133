#!/bin/bash
# SSL 问题诊断脚本 - ningmengdou.com

DOMAIN="ningmengdou.com"
CERT_PATH="/home/<USER>/software/certificate/ningmengdou.com.pem"
KEY_PATH="/home/<USER>/software/certificate/ningmengdou.com.key"

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  ningmengdou.com SSL 问题诊断工具${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 1. 检查证书文件
echo -e "${YELLOW}1. 检查证书文件...${NC}"
if [ -f "$CERT_PATH" ]; then
    echo -e "${GREEN}✓ 证书文件存在: $CERT_PATH${NC}"
    ls -la "$CERT_PATH"
else
    echo -e "${RED}✗ 证书文件不存在: $CERT_PATH${NC}"
fi

if [ -f "$KEY_PATH" ]; then
    echo -e "${GREEN}✓ 私钥文件存在: $KEY_PATH${NC}"
    ls -la "$KEY_PATH"
else
    echo -e "${RED}✗ 私钥文件不存在: $KEY_PATH${NC}"
fi
echo ""

# 2. 检查文件权限
echo -e "${YELLOW}2. 检查文件权限...${NC}"
if [ -f "$CERT_PATH" ]; then
    CERT_PERM=$(stat -c "%a" "$CERT_PATH")
    if [ "$CERT_PERM" = "644" ]; then
        echo -e "${GREEN}✓ 证书文件权限正确: $CERT_PERM${NC}"
    else
        echo -e "${RED}✗ 证书文件权限不正确: $CERT_PERM (应该是 644)${NC}"
        echo "修复命令: sudo chmod 644 $CERT_PATH"
    fi
fi

if [ -f "$KEY_PATH" ]; then
    KEY_PERM=$(stat -c "%a" "$KEY_PATH")
    if [ "$KEY_PERM" = "600" ]; then
        echo -e "${GREEN}✓ 私钥文件权限正确: $KEY_PERM${NC}"
    else
        echo -e "${RED}✗ 私钥文件权限不正确: $KEY_PERM (应该是 600)${NC}"
        echo "修复命令: sudo chmod 600 $KEY_PATH"
    fi
fi
echo ""

# 3. 验证证书内容
echo -e "${YELLOW}3. 验证证书内容...${NC}"
if [ -f "$CERT_PATH" ]; then
    if openssl x509 -in "$CERT_PATH" -noout -text >/dev/null 2>&1; then
        echo -e "${GREEN}✓ 证书文件格式正确${NC}"
        
        # 显示证书信息
        echo "证书主题:"
        openssl x509 -in "$CERT_PATH" -noout -subject
        echo "证书有效期:"
        openssl x509 -in "$CERT_PATH" -noout -dates
        
        # 检查证书是否过期
        if openssl x509 -checkend 86400 -noout -in "$CERT_PATH" >/dev/null 2>&1; then
            echo -e "${GREEN}✓ 证书未过期${NC}"
        else
            echo -e "${RED}✗ 证书已过期或即将过期${NC}"
        fi
    else
        echo -e "${RED}✗ 证书文件格式错误${NC}"
    fi
fi
echo ""

# 4. 验证私钥
echo -e "${YELLOW}4. 验证私钥...${NC}"
if [ -f "$KEY_PATH" ]; then
    if openssl rsa -in "$KEY_PATH" -check -noout >/dev/null 2>&1; then
        echo -e "${GREEN}✓ 私钥文件格式正确${NC}"
    else
        echo -e "${RED}✗ 私钥文件格式错误${NC}"
    fi
fi
echo ""

# 5. 检查证书和私钥匹配
echo -e "${YELLOW}5. 检查证书和私钥匹配...${NC}"
if [ -f "$CERT_PATH" ] && [ -f "$KEY_PATH" ]; then
    CERT_MD5=$(openssl x509 -noout -modulus -in "$CERT_PATH" | openssl md5 | cut -d' ' -f2)
    KEY_MD5=$(openssl rsa -noout -modulus -in "$KEY_PATH" | openssl md5 | cut -d' ' -f2)
    
    if [ "$CERT_MD5" = "$KEY_MD5" ]; then
        echo -e "${GREEN}✓ 证书和私钥匹配${NC}"
    else
        echo -e "${RED}✗ 证书和私钥不匹配${NC}"
        echo "证书 MD5: $CERT_MD5"
        echo "私钥 MD5: $KEY_MD5"
    fi
fi
echo ""

# 6. 检查 Nginx 配置
echo -e "${YELLOW}6. 检查 Nginx 配置...${NC}"
if nginx -t >/dev/null 2>&1; then
    echo -e "${GREEN}✓ Nginx 配置语法正确${NC}"
else
    echo -e "${RED}✗ Nginx 配置语法错误${NC}"
    echo "详细错误信息:"
    nginx -t
fi
echo ""

# 7. 检查 Nginx 服务状态
echo -e "${YELLOW}7. 检查 Nginx 服务状态...${NC}"
if systemctl is-active --quiet nginx; then
    echo -e "${GREEN}✓ Nginx 服务正在运行${NC}"
else
    echo -e "${RED}✗ Nginx 服务未运行${NC}"
    echo "启动命令: sudo systemctl start nginx"
fi
echo ""

# 8. 检查端口监听
echo -e "${YELLOW}8. 检查端口监听...${NC}"
if netstat -tlnp 2>/dev/null | grep -q ":443"; then
    echo -e "${GREEN}✓ 端口 443 正在监听${NC}"
    netstat -tlnp 2>/dev/null | grep ":443"
else
    echo -e "${RED}✗ 端口 443 未监听${NC}"
fi

if netstat -tlnp 2>/dev/null | grep -q ":80"; then
    echo -e "${GREEN}✓ 端口 80 正在监听${NC}"
else
    echo -e "${RED}✗ 端口 80 未监听${NC}"
fi
echo ""

# 9. 检查防火墙
echo -e "${YELLOW}9. 检查防火墙设置...${NC}"
if command -v ufw >/dev/null 2>&1; then
    UFW_STATUS=$(ufw status | grep -E "(80|443)")
    if [ -n "$UFW_STATUS" ]; then
        echo -e "${GREEN}✓ UFW 防火墙已允许 HTTP/HTTPS${NC}"
        echo "$UFW_STATUS"
    else
        echo -e "${RED}✗ UFW 防火墙可能阻止了 HTTP/HTTPS${NC}"
        echo "修复命令: sudo ufw allow 80 && sudo ufw allow 443"
    fi
elif command -v firewall-cmd >/dev/null 2>&1; then
    if firewall-cmd --list-services | grep -q -E "(http|https)"; then
        echo -e "${GREEN}✓ Firewalld 已允许 HTTP/HTTPS${NC}"
    else
        echo -e "${RED}✗ Firewalld 可能阻止了 HTTP/HTTPS${NC}"
        echo "修复命令: sudo firewall-cmd --permanent --add-service=http --add-service=https && sudo firewall-cmd --reload"
    fi
else
    echo -e "${YELLOW}⚠ 未检测到常见防火墙工具${NC}"
fi
echo ""

# 10. 检查域名解析
echo -e "${YELLOW}10. 检查域名解析...${NC}"
if command -v nslookup >/dev/null 2>&1; then
    RESOLVED_IP=$(nslookup $DOMAIN | grep -A1 "Name:" | tail -1 | awk '{print $2}')
    if [ -n "$RESOLVED_IP" ]; then
        echo -e "${GREEN}✓ 域名解析成功: $DOMAIN -> $RESOLVED_IP${NC}"
        
        # 检查是否解析到本机
        LOCAL_IP=$(hostname -I | awk '{print $1}')
        if [ "$RESOLVED_IP" = "$LOCAL_IP" ]; then
            echo -e "${GREEN}✓ 域名解析到本机 IP${NC}"
        else
            echo -e "${YELLOW}⚠ 域名解析到: $RESOLVED_IP, 本机 IP: $LOCAL_IP${NC}"
        fi
    else
        echo -e "${RED}✗ 域名解析失败${NC}"
    fi
else
    echo -e "${YELLOW}⚠ nslookup 命令不可用${NC}"
fi
echo ""

# 11. 测试本地连接
echo -e "${YELLOW}11. 测试本地连接...${NC}"
if curl -k -s -I https://localhost >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 本地 HTTPS 连接成功${NC}"
else
    echo -e "${RED}✗ 本地 HTTPS 连接失败${NC}"
fi

if curl -s -I http://localhost >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 本地 HTTP 连接成功${NC}"
else
    echo -e "${RED}✗ 本地 HTTP 连接失败${NC}"
fi
echo ""

# 12. 查看最近的错误日志
echo -e "${YELLOW}12. 查看最近的 Nginx 错误日志...${NC}"
if [ -f "/var/log/nginx/error.log" ]; then
    echo "最近 10 条错误日志:"
    tail -10 /var/log/nginx/error.log
else
    echo -e "${YELLOW}⚠ 未找到 Nginx 错误日志文件${NC}"
fi
echo ""

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  诊断完成${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""
echo -e "${YELLOW}常见修复步骤:${NC}"
echo "1. 修复文件权限: sudo chmod 644 $CERT_PATH && sudo chmod 600 $KEY_PATH"
echo "2. 测试配置: sudo nginx -t"
echo "3. 重启服务: sudo systemctl restart nginx"
echo "4. 检查防火墙: sudo ufw allow 80 && sudo ufw allow 443"
echo "5. 查看日志: sudo tail -f /var/log/nginx/error.log"
