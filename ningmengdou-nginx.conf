# ningmengdou.com Nginx SSL 配置
# 基于您的证书文件：ningmengdou.com.key 和 ningmengdou.com.pem

# HTTP 重定向到 HTTPS
server {
    listen 80;
    server_name ningmengdou.com www.ningmengdou.com;
    
    # 重定向所有 HTTP 请求到 HTTPS
    return 301 https://$server_name$request_uri;
}

# 主 HTTPS 服务器配置
server {
    listen 443 ssl http2;
    server_name ningmengdou.com www.ningmengdou.com;
    
    # SSL 证书配置 - 使用您的证书文件
    ssl_certificate /path/to/certificates/ningmengdou.com.pem;
    ssl_certificate_key /path/to/certificates/ningmengdou.com.key;
    
    # 现代 SSL/TLS 配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # SSL 会话配置
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_session_tickets off;
    
    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # 安全头配置
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 网站根目录
    root /var/www/ningmengdou;
    index index.html index.htm index.php;
    
    # 默认位置处理
    location / {
        try_files $uri $uri/ =404;
    }
    
    # 如果您有 PHP 应用
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # PHP 安全配置
        fastcgi_hide_header X-Powered-By;
        fastcgi_read_timeout 300;
    }
    
    # 小红书 API 代理（基于您的 Python 项目）
    location /api/xhs/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 处理小红书特殊头部
        proxy_set_header User-Agent $http_user_agent;
        proxy_set_header Referer $http_referer;
        proxy_set_header Cookie $http_cookie;
        proxy_set_header X-s $http_x_s;
        proxy_set_header X-t $http_x_t;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 通用 API 代理
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS 处理
        add_header Access-Control-Allow-Origin * always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-s,X-t" always;
        
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
    
    # 静态资源优化
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        access_log off;
    }
    
    # 安全配置 - 禁止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(py|js\.map|sql|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /50x.html {
        root /var/www/html;
    }
    
    # 日志配置
    access_log /var/log/nginx/ningmengdou_access.log combined;
    error_log /var/log/nginx/ningmengdou_error.log warn;
}

# 如果需要子域名配置（如 api.ningmengdou.com）
server {
    listen 443 ssl http2;
    server_name api.ningmengdou.com;
    
    # 使用相同的证书（如果是通配符证书）
    ssl_certificate /path/to/certificates/ningmengdou.com.pem;
    ssl_certificate_key /path/to/certificates/ningmengdou.com.key;
    
    # SSL 配置同上
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    
    # 专门用于 API 服务
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # API 专用头部
        proxy_set_header Content-Type $http_content_type;
        proxy_set_header Authorization $http_authorization;
    }
    
    # 日志
    access_log /var/log/nginx/api_ningmengdou_access.log;
    error_log /var/log/nginx/api_ningmengdou_error.log;
}
